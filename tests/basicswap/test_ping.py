#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2024-2025 The Basicswap developers
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

import os
import sys
import time
import tempfile
import shutil

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))

from basicswap.basicswap import BasicSwap
from basicswap.basicswap_util import OffererPingStatus, MessageTypes
from basicswap.db import OffererPingStatus as OffererPingStatusDB
from basicswap.messages_npb import OffererPingMessage


def test_ping_system():
    """Test the ping system functionality."""
    print("Testing BasicSwap Offerer Ping System")

    print("\n1. Testing message types...")
    assert hasattr(MessageTypes, "OFFERER_PING")
    print("Ping message types defined correctly")

    print("\n2. Testing message classes...")
    ping_msg = OffererPingMessage()
    ping_msg.timestamp = int(time.time())
    ping_msg.protocol_version = 5
    ping_msg.active_offers_count = 3

    ping_bytes = ping_msg.to_bytes()
    assert len(ping_bytes) > 0

    ping_msg2 = OffererPingMessage()
    ping_msg2.from_bytes(ping_bytes)
    assert ping_msg2.timestamp == ping_msg.timestamp
    assert ping_msg2.active_offers_count == ping_msg.active_offers_count
    print("Ping message serialization/deserialization works")

    print("Offerer ping system basic functionality works correctly.")

        return True

    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_ping_system()
    sys.exit(0 if success else 1)
