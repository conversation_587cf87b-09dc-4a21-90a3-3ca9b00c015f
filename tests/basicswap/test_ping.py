#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2024-2025 The Basicswap developers
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

import os
import sys
import time
import tempfile
import shutil

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))

from basicswap.basicswap import BasicSwap
from basicswap.basicswap_util import OffererPingStatus, MessageTypes
from basicswap.db import OffererPingStatus as OffererPingStatusDB
from basicswap.messages_npb import OffererPingMessage


def test_ping_system():
    """Test the ping system functionality."""
    print("Testing BasicSwap Offerer Ping System")
    print("=" * 50)

    temp_dir1 = tempfile.mkdtemp()
    temp_dir2 = tempfile.mkdtemp()
    print(f"Using temporary directories: {temp_dir1}, {temp_dir2}")

    try:
        # Settings for first instance (offerer)
        settings1 = {
            "network_key": "7sW2UEcHXxuqEZQzjbqvqr5Y6hs2fmwWqEJbqkLcnQNQrTrHdSz",
            "network_pubkey": "02279974f07f75e9dab75e3a7ab1a58e1e56d8b5b6c6b1e6c6b1e6c6b1e6c6b1e6",
            "zmqhost": "tcp://127.0.0.1",
            "zmqport": 20792,
            "htmlhost": "127.0.0.1",
            "htmlport": 12700,
            "debug": True,
            "debug_ui": True,
            "prune_inactive_offers": True,
        }

        # Settings for second instance (user)
        settings2 = {
            "network_key": "8tX3VFdRYyxrqEZQzjbqvqr5Y6hs2fmwWqEJbqkLcnQNQrTrHdSz",
            "network_pubkey": "03389974f07f75e9dab75e3a7ab1a58e1e56d8b5b6c6b1e6c6b1e6c6b1e6c6b1e7",
            "zmqhost": "tcp://127.0.0.1",
            "zmqport": 20793,
            "htmlhost": "127.0.0.1",
            "htmlport": 12701,
            "debug": True,
            "debug_ui": True,
            "prune_inactive_offers": True,
        }

        print("\nCreating BasicSwap instances...")
        swap_client1 = BasicSwap(temp_dir1, settings1, "regtest")  # Offerer
        swap_client2 = BasicSwap(temp_dir2, settings2, "regtest")  # User
        print("Created offerer and user instances")

        print("\n1. Testing ping configuration...")
        assert hasattr(swap_client1, "offerer_ping_seconds")
        assert hasattr(swap_client1, "offerer_ping_timeout_seconds")
        assert hasattr(swap_client1, "offerer_ping_prune_after_seconds")
        assert hasattr(swap_client1, "prune_inactive_offers")
        assert hasattr(swap_client2, "offerer_ping_seconds")
        print("Ping configuration settings loaded correctly")

        print("\n2. Testing ping tracking variables...")
        assert hasattr(swap_client1, "_last_sent_ping")
        assert hasattr(swap_client1, "_last_checked_pings")
        assert hasattr(swap_client2, "_last_sent_ping")
        assert hasattr(swap_client2, "_last_checked_pings")
        print("Ping tracking variables initialized correctly")

        print("\n3. Testing message types...")
        assert hasattr(MessageTypes, "OFFERER_PING")
        print("Ping message types defined correctly")

        print("\n4. Testing message classes...")
        ping_msg = OffererPingMessage()
        ping_msg.timestamp = int(time.time())
        ping_msg.protocol_version = 5
        ping_msg.active_offers_count = 3

        ping_bytes = ping_msg.to_bytes()
        assert len(ping_bytes) > 0

        ping_msg2 = OffererPingMessage()
        ping_msg2.from_bytes(ping_bytes)
        assert ping_msg2.timestamp == ping_msg.timestamp
        assert ping_msg2.active_offers_count == ping_msg.active_offers_count
        print("Ping message serialization/deserialization works")

        print("\n5. Testing database operations...")
        cursor1 = swap_client1.openDB()
        try:
            test_addr = swap_client2.network_addr
            ping_status = swap_client1.getOrCreateOffererPingStatus(test_addr, cursor1)
            assert ping_status is not None
            assert ping_status.addr_from == test_addr
            assert ping_status.status == OffererPingStatus.UNKNOWN
            print("Database ping status creation works")

            ping_status.status = OffererPingStatus.ONLINE
            ping_status.ping_failures = 0
            ping_status.last_ping_received = int(time.time())
            print("Database ping status update works")

        finally:
            swap_client1.closeDB(cursor1)

        print("\n6. Testing ping statistics...")
        stats1 = swap_client1.getOffererPingStats()
        stats2 = swap_client2.getOffererPingStats()
        assert isinstance(stats1, dict)
        assert isinstance(stats2, dict)
        assert "unknown" in stats1
        assert "online" in stats1
        print("Ping statistics generation works")

        print("\n7. Testing ping message processing...")
        print("  - Creating mock ping message...")

        # Create a mock ping message from offerer to user
        mock_ping = {
            "hex": "0a" + OffererPingMessage().to_bytes().hex(),
            "from": swap_client1.network_addr,
            "to": "",
            "received": int(time.time())
        }

        print("  - Testing ping message processing...")
        cursor2 = swap_client2.openDB()
        try:
            # Process the mock ping on the user instance
            swap_client2.processOffererPing(mock_ping)

            # Check if ping status was updated
            ping_status = swap_client2.getOrCreateOffererPingStatus(swap_client1.network_addr, cursor2)
            assert ping_status.status == OffererPingStatus.ONLINE
            print("  - Ping message processing works")

        finally:
            swap_client2.closeDB(cursor2)

        print("\n8. Testing ping timeout and pruning logic...")
        cursor2 = swap_client2.openDB()
        try:
            # Create old ping status to test timeout
            old_ping_status = swap_client2.getOrCreateOffererPingStatus("old_offerer_addr", cursor2)
            old_ping_status.last_ping_received = int(time.time()) - (15 * 60)  # 15 minutes ago
            old_ping_status.status = OffererPingStatus.ONLINE

            # Test timeout detection
            swap_client2.checkPings()

            # Verify status changed to offline/unresponsive
            updated_status = swap_client2.getOrCreateOffererPingStatus("old_offerer_addr", cursor2)
            assert updated_status.status in [OffererPingStatus.OFFLINE, OffererPingStatus.UNRESPONSIVE]
            print("  - Ping timeout detection works")

        finally:
            swap_client2.closeDB(cursor2)

        print("Ping system logic testing completed successfully")

        print("Offerer ping system is working correctly.")

        return True

    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False

    finally:
        try:
            shutil.rmtree(temp_dir1)
            shutil.rmtree(temp_dir2)
            print(f"\nCleaned up temporary directories: {temp_dir1}, {temp_dir2}")
        except Exception as e:
            print(f"Warning: Could not clean up temp directories: {e}")


if __name__ == "__main__":
    success = test_ping_system()
    sys.exit(0 if success else 1)
