#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2024-2025 The Basicswap developers
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

import os
import sys
import time
import unittest
import logging

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))

from basicswap.basicswap_util import OffererPingStatus, MessageTypes
from basicswap.messages_npb import OffererPingMessage
import basicswap.config as cfg

# Import BaseTest from test_xmr
test_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, test_dir)
from test_xmr import BaseTest, test_delay_event, signal_event, TEST_DIR


class TestPingSystem(BaseTest):
    __test__ = True
    start_ltc_nodes = False
    start_xmr_nodes = True  # Enable XMR nodes
    start_btc_nodes = False

    @classmethod
    def addCoinSettings(cls, settings, datadir, node_id):
        # Use Particl and XMR only
        if cls.start_xmr_nodes:
            settings["chainclients"]["monero"] = {
                "connection_type": "rpc",
                "manage_daemon": False,
                "rpcport": 29798 + node_id,
                "rpcuser": "test" + str(node_id),
                "rpcpassword": "test_pass" + str(node_id),
                "walletrpcport": 29998 + node_id,
                "walletrpcuser": "test" + str(node_id),
                "walletrpcpassword": "test_pass" + str(node_id),
                "datadir": os.path.join(datadir, "xmr_" + str(node_id)),
                "bindir": cfg.XMR_BINDIR,
                "restore_height": 0,
                "blocks_confirmed": 2,
                "anon_tx_ring_size": 5,
            }

    @classmethod
    def setUpClass(cls):
        # Custom setup that skips Bitcoin nodes entirely
        import sys
        import signal
        import logging
        import shutil
        from tests.basicswap.common import (
            prepareDataDir, make_rpc_func, waitForRPC, stopDaemons
        )
        from basicswap.bin.run import startDaemon
        from tests.basicswap.test_xmr import prepare_swapclient_dir
        import basicswap.config as cfg
        from basicswap.basicswap import Coins

        if signal_event.is_set():
            raise ValueError("Test has been cancelled.")
        test_delay_event.clear()

        logger = logging.getLogger()
        logger.propagate = False
        logger.handlers = []
        logger.setLevel(logging.INFO)
        formatter = logging.Formatter("%(asctime)s %(levelname)s : %(message)s")
        stream_stdout = logging.StreamHandler(sys.stdout)
        stream_stdout.setFormatter(formatter)
        logger.addHandler(stream_stdout)

        logging.info("Setting up tests for class: " + cls.__name__)

        if os.path.isdir(TEST_DIR):
            logging.info("Removing test dir " + TEST_DIR)
            for name in os.listdir(TEST_DIR):
                if name == "pivx-params":
                    continue
                fullpath = os.path.join(TEST_DIR, name)
                if os.path.isdir(fullpath):
                    shutil.rmtree(fullpath)
                else:
                    os.remove(fullpath)
        else:
            logging.info("Creating test dir " + TEST_DIR)
        if not os.path.exists(TEST_DIR):
            os.makedirs(TEST_DIR)

        cls.stream_fp = logging.FileHandler(os.path.join(TEST_DIR, "test.log"))
        cls.stream_fp.setFormatter(formatter)
        logger.addHandler(cls.stream_fp)

        cls.prepareTestDir()

        try:
            # Start only Particl nodes (skip Bitcoin completely)
            logging.info("Preparing Particl nodes only.")

            # Start Particl nodes
            for i in range(cls.num_nodes):
                data_dir = prepareDataDir(TEST_DIR, i, "particl.conf", "part_")

                cls.part_daemons.append(
                    startDaemon(
                        os.path.join(TEST_DIR, "part_" + str(i)),
                        cfg.PARTICL_BINDIR,
                        cfg.PARTICLD,
                    )
                )
                logging.info("Started %s %d", cfg.PARTICLD, cls.part_daemons[-1].handle.pid)

            # Wait for Particl nodes and set up wallets
            for i in range(cls.num_nodes):
                rpc = make_rpc_func(i)
                waitForRPC(rpc, test_delay_event)
                if i == 0:
                    rpc("extkeyimportmaster", ["abandon baby cabbage dad eager fabric gadget habit ice kangaroo lab absorb"])
                elif i == 1:
                    rpc("extkeyimportmaster", ["pact mammal barrel matrix local final lecture chunk wasp survey bid various book strong spread fall ozone daring like topple door fatigue limb olympic", "", "true"])
                    rpc("getnewextaddress", ["lblExtTest"])
                    rpc("rescanblockchain")
                else:
                    rpc("extkeyimportmaster", [rpc("mnemonic", ["new"])["master"]])
                rpc("walletsettings", ["stakingoptions", {"stakecombinethreshold": 100, "stakesplitthreshold": 200}])
                rpc("reservebalance", [False])

            # Start XMR nodes if enabled
            if cls.start_xmr_nodes:
                cls.startXmrNodes()

            # Prepare swap client directories (Particl + XMR only)
            for i in range(cls.num_nodes):
                prepare_swapclient_dir(
                    TEST_DIR,
                    i,
                    cls.getRandomPubkey().hex(),
                    cls.getRandomPubkey().hex(),
                    with_coins={Coins.XMR} if cls.start_xmr_nodes else set(),
                    cls=cls,
                )

            # Start swap clients
            cls.startSwapClients()

        except Exception as e:
            logging.error(f"setUpClass failed: {e}")
            cls.tearDownClass()
            raise ValueError("setUpClass() failed.")
    def test_01_ping_message_processing(self):
        """Test ping message creation and processing."""
        logging.info("Testing ping message processing")

        # Test message creation
        ping_msg = OffererPingMessage()
        ping_msg.timestamp = int(time.time())
        ping_msg.protocol_version = 5
        ping_msg.active_offers_count = 3

        ping_bytes = ping_msg.to_bytes()
        assert len(ping_bytes) > 0

        # Test message deserialization
        ping_msg2 = OffererPingMessage()
        ping_msg2.from_bytes(ping_bytes)
        assert ping_msg2.timestamp == ping_msg.timestamp
        assert ping_msg2.active_offers_count == ping_msg.active_offers_count
        logging.info("Ping message serialization/deserialization works")

    def test_02_ping_configuration(self):
        """Test ping system configuration."""
        logging.info("Testing ping configuration")

        swap_clients = self.swap_clients

        # Test configuration on all clients
        for i, client in enumerate(swap_clients):
            assert hasattr(client, "offerer_ping_seconds")
            assert hasattr(client, "offerer_ping_timeout_seconds")
            assert hasattr(client, "offerer_ping_prune_after_seconds")
            assert hasattr(client, "prune_inactive_offers")
            assert hasattr(client, "_last_sent_ping")
            assert hasattr(client, "_last_checked_pings")
            logging.info(f"Client {i} ping configuration correct")

    def test_03_ping_database_operations(self):
        """Test ping database operations."""
        logging.info("Testing ping database operations")

        swap_clients = self.swap_clients
        client = swap_clients[0]

        cursor = client.openDB()
        try:
            test_addr = "test_address_123"
            ping_status = client.getOrCreateOffererPingStatus(test_addr, cursor)
            assert ping_status is not None
            assert ping_status.addr_from == test_addr
            assert ping_status.status == OffererPingStatus.UNKNOWN
            logging.info("Database ping status creation works")

            ping_status.status = OffererPingStatus.ONLINE
            ping_status.ping_failures = 0
            ping_status.last_ping_received = int(time.time())
            logging.info("Database ping status update works")

        finally:
            client.closeDB(cursor)

    def test_04_ping_statistics(self):
        """Test ping statistics generation."""
        logging.info("Testing ping statistics")

        swap_clients = self.swap_clients

        for i, client in enumerate(swap_clients):
            stats = client.getOffererPingStats()
            assert isinstance(stats, dict)
            assert "unknown" in stats
            assert "online" in stats
            assert "offline" in stats
            assert "unresponsive" in stats
            logging.info(f"Client {i} ping statistics work")

    def test_05_ping_processing_with_real_network(self):
        """Test ping processing between real BasicSwap instances."""
        logging.info("Testing ping processing with real network")

        swap_clients = self.swap_clients
        client1 = swap_clients[0]  # Offerer
        client2 = swap_clients[1]  # User

        # Create a mock ping message from client1 to client2
        mock_ping = {
            "hex": "0a" + OffererPingMessage().to_bytes().hex(),
            "from": client1.network_addr,
            "to": "",
            "received": int(time.time())
        }

        # Process the mock ping on client2
        cursor = client2.openDB()
        try:
            client2.processOffererPing(mock_ping)

            # Check if ping status was updated
            ping_status = client2.getOrCreateOffererPingStatus(client1.network_addr, cursor)
            assert ping_status.status == OffererPingStatus.ONLINE
            logging.info("Ping message processing between real instances works")

        finally:
            client2.closeDB(cursor)

    def test_06_ping_timeout_detection(self):
        """Test ping timeout and pruning logic."""
        logging.info("Testing ping timeout detection")

        swap_clients = self.swap_clients
        client = swap_clients[0]

        cursor = client.openDB()
        try:
            # Create old ping status to test timeout
            old_ping_status = client.getOrCreateOffererPingStatus("old_offerer_addr", cursor)
            old_ping_status.last_ping_received = int(time.time()) - (15 * 60)  # 15 minutes ago
            old_ping_status.status = OffererPingStatus.ONLINE

            # Test timeout detection
            client.checkPings()

            # Verify status changed to offline/unresponsive
            updated_status = client.getOrCreateOffererPingStatus("old_offerer_addr", cursor)
            assert updated_status.status in [OffererPingStatus.OFFLINE, OffererPingStatus.UNRESPONSIVE]
            logging.info("Ping timeout detection works")

        finally:
            client.closeDB(cursor)


if __name__ == "__main__":
    unittest.main()
