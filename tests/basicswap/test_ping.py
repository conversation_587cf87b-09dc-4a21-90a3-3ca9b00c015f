#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2024-2025 The Basicswap developers
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

import os
import sys
import time
import unittest
import logging

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))

from basicswap.basicswap_util import OffererPingStatus, MessageTypes
from basicswap.messages_npb import OffererPingMessage

# Import BaseTest from test_xmr
test_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, test_dir)
from test_xmr import BaseTest, test_delay_event


class TestPingSystem(BaseTest):
    __test__ = True
    def test_01_ping_message_processing(self):
        """Test ping message creation and processing."""
        logging.info("Testing ping message processing")

        # Test message creation
        ping_msg = OffererPingMessage()
        ping_msg.timestamp = int(time.time())
        ping_msg.protocol_version = 5
        ping_msg.active_offers_count = 3

        ping_bytes = ping_msg.to_bytes()
        assert len(ping_bytes) > 0

        # Test message deserialization
        ping_msg2 = OffererPingMessage()
        ping_msg2.from_bytes(ping_bytes)
        assert ping_msg2.timestamp == ping_msg.timestamp
        assert ping_msg2.active_offers_count == ping_msg.active_offers_count
        logging.info("Ping message serialization/deserialization works")

    def test_02_ping_configuration(self):
        """Test ping system configuration."""
        logging.info("Testing ping configuration")

        swap_clients = self.swap_clients

        # Test configuration on all clients
        for i, client in enumerate(swap_clients):
            assert hasattr(client, "offerer_ping_seconds")
            assert hasattr(client, "offerer_ping_timeout_seconds")
            assert hasattr(client, "offerer_ping_prune_after_seconds")
            assert hasattr(client, "prune_inactive_offers")
            assert hasattr(client, "_last_sent_ping")
            assert hasattr(client, "_last_checked_pings")
            logging.info(f"Client {i} ping configuration correct")

    def test_03_ping_database_operations(self):
        """Test ping database operations."""
        logging.info("Testing ping database operations")

        swap_clients = self.swap_clients
        client = swap_clients[0]

        cursor = client.openDB()
        try:
            test_addr = "test_address_123"
            ping_status = client.getOrCreateOffererPingStatus(test_addr, cursor)
            assert ping_status is not None
            assert ping_status.addr_from == test_addr
            assert ping_status.status == OffererPingStatus.UNKNOWN
            logging.info("Database ping status creation works")

            ping_status.status = OffererPingStatus.ONLINE
            ping_status.ping_failures = 0
            ping_status.last_ping_received = int(time.time())
            logging.info("Database ping status update works")

        finally:
            client.closeDB(cursor)

    def test_04_ping_statistics(self):
        """Test ping statistics generation."""
        logging.info("Testing ping statistics")

        swap_clients = self.swap_clients

        for i, client in enumerate(swap_clients):
            stats = client.getOffererPingStats()
            assert isinstance(stats, dict)
            assert "unknown" in stats
            assert "online" in stats
            assert "offline" in stats
            assert "unresponsive" in stats
            logging.info(f"Client {i} ping statistics work")

    def test_05_ping_processing_with_real_network(self):
        """Test ping processing between real BasicSwap instances."""
        logging.info("Testing ping processing with real network")

        swap_clients = self.swap_clients
        client1 = swap_clients[0]  # Offerer
        client2 = swap_clients[1]  # User

        # Create a mock ping message from client1 to client2
        mock_ping = {
            "hex": "0a" + OffererPingMessage().to_bytes().hex(),
            "from": client1.network_addr,
            "to": "",
            "received": int(time.time())
        }

        # Process the mock ping on client2
        cursor = client2.openDB()
        try:
            client2.processOffererPing(mock_ping)

            # Check if ping status was updated
            ping_status = client2.getOrCreateOffererPingStatus(client1.network_addr, cursor)
            assert ping_status.status == OffererPingStatus.ONLINE
            logging.info("Ping message processing between real instances works")

        finally:
            client2.closeDB(cursor)

    def test_06_ping_timeout_detection(self):
        """Test ping timeout and pruning logic."""
        logging.info("Testing ping timeout detection")

        swap_clients = self.swap_clients
        client = swap_clients[0]

        cursor = client.openDB()
        try:
            # Create old ping status to test timeout
            old_ping_status = client.getOrCreateOffererPingStatus("old_offerer_addr", cursor)
            old_ping_status.last_ping_received = int(time.time()) - (15 * 60)  # 15 minutes ago
            old_ping_status.status = OffererPingStatus.ONLINE

            # Test timeout detection
            client.checkPings()

            # Verify status changed to offline/unresponsive
            updated_status = client.getOrCreateOffererPingStatus("old_offerer_addr", cursor)
            assert updated_status.status in [OffererPingStatus.OFFLINE, OffererPingStatus.UNRESPONSIVE]
            logging.info("Ping timeout detection works")

        finally:
            client.closeDB(cursor)


if __name__ == "__main__":
    unittest.main()
