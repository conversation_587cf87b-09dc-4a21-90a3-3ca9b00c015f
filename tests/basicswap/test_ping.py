#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2024-2025 The Basicswap developers
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

import os
import sys
import time
import tempfile
import shutil

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))

from basicswap.basicswap import BasicSwap
from basicswap.basicswap_util import OffererHeartbeatStatus, MessageTypes
from basicswap.db import OffererHeartbeatStatus as OffererHeartbeatStatusDB
from basicswap.messages_npb import OffererHeartbeatMessage


def test_ping_system():
    """Test the heartbeat system functionality."""
    print("Testing BasicSwap Offerer Heartbeat System")
    print("=" * 50)

    temp_dir = tempfile.mkdtemp()
    print(f"Using temporary directory: {temp_dir}")

    try:
        settings = {
            "network_key": "7sW2UEcHXxuqEZQzjbqvqr5Y6hs2fmwWqEJbqkLcnQNQrTrHdSz",
            "network_pubkey": "02279974f07f75e9dab75e3a7ab1a58e1e56d8b5b6c6b1e6c6b1e6c6b1e6c6b1e6",
            "zmqhost": "tcp://127.0.0.1",
            "zmqport": 20792,
            "htmlhost": "127.0.0.1",
            "htmlport": 12700,
            "debug": True,
            "debug_ui": True,
        }

        swap_client = BasicSwap(temp_dir, settings, "regtest")

        print("\n1. Testing heartbeat configuration...")
        assert hasattr(swap_client, "offerer_heartbeat_seconds")
        assert hasattr(swap_client, "offerer_heartbeat_timeout_seconds")
        assert hasattr(swap_client, "offerer_heartbeat_prune_after_seconds")
        print("Heartbeat configuration settings loaded correctly")

        print("\n2. Testing heartbeat tracking variables...")
        assert hasattr(swap_client, "_last_sent_heartbeat")
        assert hasattr(swap_client, "_last_checked_heartbeats")
        print("Heartbeat tracking variables initialized correctly")

        print("\n3. Testing message types...")
        assert hasattr(MessageTypes, "OFFERER_HEARTBEAT")
        print("Heartbeat message types defined correctly")

        print("\n4. Testing message classes...")
        heartbeat_msg = OffererHeartbeatMessage()

        heartbeat_msg.timestamp = int(time.time())
        heartbeat_msg.protocol_version = 5
        heartbeat_msg.active_offers_count = 3

        heartbeat_bytes = heartbeat_msg.to_bytes()
        assert len(heartbeat_bytes) > 0

        heartbeat_msg2 = OffererHeartbeatMessage()
        heartbeat_msg2.from_bytes(heartbeat_bytes)
        assert heartbeat_msg2.timestamp == heartbeat_msg.timestamp
        assert heartbeat_msg2.active_offers_count == heartbeat_msg.active_offers_count
        print("Heartbeat message serialization/deserialization works")

        print("\n5. Testing database model...")
        cursor = swap_client.openDB()
        try:
            test_addr = "test_address_123"
            heartbeat_status = swap_client.getOrCreateOffererHeartbeatStatus(test_addr, cursor)
            assert heartbeat_status is not None
            assert heartbeat_status.addr_from == test_addr
            assert heartbeat_status.status == OffererHeartbeatStatus.UNKNOWN
            print("Database heartbeat status creation works")

            heartbeat_status.status = OffererHeartbeatStatus.ONLINE
            heartbeat_status.heartbeat_failures = 0
            swap_client.add(heartbeat_status, cursor, upsert=True)

            retrieved_status = swap_client.getOrCreateOffererHeartbeatStatus(
                test_addr, cursor
            )
            assert retrieved_status.status == OffererHeartbeatStatus.ONLINE
            print("Database heartbeat status update works")

        finally:
            swap_client.closeDB(cursor)

        print("\n6. Testing heartbeat statistics...")
        stats = swap_client.getOffererHeartbeatStats()
        assert isinstance(stats, dict)
        assert "unknown" in stats
        assert "online" in stats
        assert "offline" in stats
        assert "unresponsive" in stats
        print("Heartbeat statistics generation works")

        print("Offerer heartbeat system is working correctly.")

        return True

    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False

    finally:
        try:
            shutil.rmtree(temp_dir)
            print(f"\nCleaned up temporary directory: {temp_dir}")
        except Exception as e:
            print(f"Warning: Could not clean up temp directory: {e}")


if __name__ == "__main__":
    success = test_ping_system()
    sys.exit(0 if success else 1)
