#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2024-2025 The Basicswap developers
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

import os
import sys
import time
import tempfile
import shutil
import unittest
import logging

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))

from basicswap.basicswap import BasicSwap
from basicswap.basicswap_util import OffererPingStatus, MessageTypes
from basicswap.messages_npb import OffererPingMessage


class TestPingSystem(unittest.TestCase):

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()

        # Create a minimal BasicSwap instance for testing
        settings = {
            "network_key": "7sW2UEcHXxuqEZQzjbqvqr5Y6hs2fmwWqEJbqkLcnQNQrTrHdSz",
            "network_pubkey": "02279974f07f75e9dab75e3a7ab1a58e1e56d8b5b6c6b1e6c6b1e6c6b1e6c6b1e6",
            "zmqhost": "tcp://127.0.0.1",
            "zmqport": 20792,
            "htmlhost": "127.0.0.1",
            "htmlport": 12700,
            "debug": True,
            "debug_ui": True,
            "prune_inactive_offers": True,
        }

        # Create BasicSwap instance without starting daemons
        self.swap_client = BasicSwap(self.temp_dir, settings, "regtest")

    def tearDown(self):
        """Clean up test fixtures."""
        try:
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            logging.warning(f"Could not clean up temp directory: {e}")

    def test_01_ping_message_processing(self):
        """Test ping message creation and processing."""
        logging.info("Testing ping message processing")

        # Test message creation
        ping_msg = OffererPingMessage()
        ping_msg.timestamp = int(time.time())
        ping_msg.protocol_version = 5
        ping_msg.active_offers_count = 3

        ping_bytes = ping_msg.to_bytes()
        self.assertGreater(len(ping_bytes), 0)

        # Test message deserialization
        ping_msg2 = OffererPingMessage()
        ping_msg2.from_bytes(ping_bytes)
        self.assertEqual(ping_msg2.timestamp, ping_msg.timestamp)
        self.assertEqual(ping_msg2.active_offers_count, ping_msg.active_offers_count)
        logging.info("Ping message serialization/deserialization works")

    def test_02_ping_configuration(self):
        """Test ping system configuration."""
        logging.info("Testing ping configuration")

        client = self.swap_client

        # Test configuration
        self.assertTrue(hasattr(client, "offerer_ping_seconds"))
        self.assertTrue(hasattr(client, "offerer_ping_timeout_seconds"))
        self.assertTrue(hasattr(client, "offerer_ping_prune_after_seconds"))
        self.assertTrue(hasattr(client, "prune_inactive_offers"))
        self.assertTrue(hasattr(client, "_last_sent_ping"))
        self.assertTrue(hasattr(client, "_last_checked_pings"))
        logging.info("Ping configuration correct")

    def test_03_ping_database_operations(self):
        """Test ping database operations."""
        logging.info("Testing ping database operations")

        client = self.swap_client

        cursor = client.openDB()
        try:
            test_addr = "test_address_123"
            ping_status = client.getOrCreateOffererPingStatus(test_addr, cursor)
            self.assertIsNotNone(ping_status)
            self.assertEqual(ping_status.addr_from, test_addr)
            self.assertEqual(ping_status.status, OffererPingStatus.UNKNOWN)
            logging.info("Database ping status creation works")

            ping_status.status = OffererPingStatus.ONLINE
            ping_status.ping_failures = 0
            ping_status.last_ping_received = int(time.time())
            logging.info("Database ping status update works")

        finally:
            client.closeDB(cursor)

    def test_04_ping_statistics(self):
        """Test ping statistics generation."""
        logging.info("Testing ping statistics")

        client = self.swap_client

        stats = client.getOffererPingStats()
        self.assertIsInstance(stats, dict)
        self.assertIn("unknown", stats)
        self.assertIn("online", stats)
        self.assertIn("offline", stats)
        self.assertIn("unresponsive", stats)
        logging.info("Ping statistics work")

    def test_05_ping_message_types(self):
        """Test ping message types are defined."""
        logging.info("Testing ping message types")

        self.assertTrue(hasattr(MessageTypes, "OFFERER_PING"))
        logging.info("Ping message types defined correctly")

    def test_06_ping_processing_mock(self):
        """Test ping processing with mock data."""
        logging.info("Testing ping processing with mock data")

        client = self.swap_client

        # Create a mock ping message
        mock_ping = {
            "hex": "0a" + OffererPingMessage().to_bytes().hex(),
            "from": "test_offerer_address",
            "to": "",
            "received": int(time.time())
        }

        # Process the mock ping
        cursor = client.openDB()
        try:
            client.processOffererPing(mock_ping)

            # Check if ping status was updated
            ping_status = client.getOrCreateOffererPingStatus("test_offerer_address", cursor)
            self.assertEqual(ping_status.status, OffererPingStatus.ONLINE)
            logging.info("Ping message processing works")

        finally:
            client.closeDB(cursor)


if __name__ == "__main__":
    unittest.main()
